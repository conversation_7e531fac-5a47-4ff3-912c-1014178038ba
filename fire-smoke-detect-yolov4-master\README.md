## fire-smoke-detect-yolov4-v5 and fire-smoke-detect-dataset

* author is leilei
* [**README_ZN 中文版说明**](./readmes/README_ZN.md)
* [**README_EN English Description**](./readmes/README_EN.md)
* [**yolov4 tensorrt python inference**](https://github.com/gengyanlei/onnx2tensorRt)
* [**Note: 百度Paddle智慧城市生态使用本人烟火检测数据集(PS:明明是我的数据还要感谢别人一下)**](https://github.com/PaddlePaddle/awesome-DeepLearning/tree/master/Paddle_Industry_Practice_Sample_Library/Fire_and_Smoke_Detection)

* This repository code has stopped updating, please use the dataset to retrain the detection model directly!
* This repository code has stopped updating, please use the dataset to retrain the detection model directly!
* This repository code has stopped updating, please use the dataset to retrain the detection model directly!

### fire-smoke-detect-demo
|![fire-smoke-detect-demo](./result/result_demo.jpg)|
|----|

### Data Label Tool
+ [CVAT](https://github.com/openvinotoolkit/cvat)
+ [CVAT-Tutorial](https://blog.csdn.net/LEILEI18A/article/details/113385510)

### Other
* [leilei's blog](https://blog.csdn.net/LEILEI18A/article/details/107334474)
* [VSCode Remote SSH 安装教程](https://blog.csdn.net/LEILEI18A/article/details/102524181)
* [segmentation_pytorch 语义分割](https://github.com/gengyanlei/segmentation_pytorch)
* [building-segmentation-dataset 遥感影像建筑语义分割](https://github.com/gengyanlei/build_segmentation_dataset)
* [reflective-clothes-detect-dataset 安全帽反光衣检测](https://github.com/gengyanlei/reflective-clothes-detect)
