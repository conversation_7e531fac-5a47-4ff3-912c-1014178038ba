

### Datasets:

59.26TB of research data: http://academictorrents.com/ 

ImageNet Torrent: http://academictorrents.com/browse.php?search=imagenet&page=0

25 thousand datasets on Kaggle: https://www.kaggle.com/datasets

BDD100K - Diverse Driving Video: https://bair.berkeley.edu/blog/2018/05/30/bdd/

Pascal VOC: http://host.robots.ox.ac.uk/pascal/VOC/voc2012/index.html

MS COCO: http://cocodataset.org/#download

ImageNet: http://imagenet.stanford.edu/download.php

ImageNet (ILSVRC2012): http://www.image-net.org/challenges/LSVRC/2012/nonpub-downloads

ImageNet (ILSVRC2015): http://image-net.org/small/download.php

ImageNet VID: http://bvisionweb1.cs.unc.edu/ilsvrc2015/download-videos-3j16.php

Open Images: https://storage.googleapis.com/openimages/web/download.html

Cityscapes: https://www.cityscapes-dataset.com/

Object Tracking Benchmark: http://cvlab.hanyang.ac.kr/tracker_benchmark/datasets.html

MOT (Multiple object tracking benchmark): https://motchallenge.net/

VOT (Visual object tracking): http://www.votchallenge.net/challenges.html

FREE FLIR Thermal Dataset (infrared): https://www.flir.eu/oem/adas/adas-dataset-form/

MARS: http://www.liangzheng.com.cn/Project/project_mars.html

Market-1501: http://www.liangzheng.org/Project/project_reid.html

German Traffic Sign Recognition Benchmark: http://benchmark.ini.rub.de/

Labeled Faces in the Wild: http://vis-www.cs.umass.edu/lfw/

Core50: https://vlomonaco.github.io/core50/

Visual Question Answering: https://visualqa.org/download.html

Large Movie Review Dataset: http://ai.stanford.edu/~amaas/data/sentiment/

KITTI (for autonomous driving): http://www.cvlibs.net/datasets/kitti/

nuScenes (for autonomous driving): https://www.nuscenes.org/overview

----

Wikipedia's List of datasets: https://en.wikipedia.org/wiki/List_of_datasets_for_machine-learning_research

Other datasets (Music, Natural Images, Artificial Datasets, Faces, Text, Speech, Recommendation Systems, Misc): http://deeplearning.net/datasets/

25 datasets: https://www.analyticsvidhya.com/blog/2018/03/comprehensive-collection-deep-learning-datasets/

List of datasets: https://riemenschneider.hayko.at/vision/dataset/index.php

Another list of datasets: http://homepages.inf.ed.ac.uk/rbf/CVonline/Imagedbase.htm

Pedestrian DATASETs for Vision based Detection and Tracking: https://hemprasad.wordpress.com/2014/11/08/pedestrian-datasets-for-vision-based-detection-and-tracking/

TrackingNet: https://tracking-net.org/

RGB, RGBD, Texture-mapped 3D mesh models: http://www.ycbbenchmarks.com/