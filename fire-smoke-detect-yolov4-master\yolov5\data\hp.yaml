# train and val data as 1) directory: path/images/, 2) file: path/images.txt, or 3) list: [path1/images/, path2/images/]
# 这里是基于SHWD的 安全帽(2类)+person(1类) 共3类进行，后面会加上工作服(反光衣2类) 攻击5类进行
train: /home/<USER>/dataset/helmet_dataset/VOC2028/txt_yolov5/2028_trainval.txt
val: /home/<USER>/dataset/helmet_dataset/VOC2028/txt_yolov5/2028_test.txt

# number of classes
nc: 3

# class names
names: ['hat', 'head', 'person']